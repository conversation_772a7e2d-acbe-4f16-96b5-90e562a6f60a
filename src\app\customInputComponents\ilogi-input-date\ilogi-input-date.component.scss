.form-group {
  margin-bottom: 1rem;
}
label {
  margin-bottom: 0.5rem; /* Controls space between label and input */
}
.bold-label {
  font-weight: 500 !important; /* Bold when readonly */
}

.form-control.date-input {
  border: 1px solid var(--form-input-border);
  border-radius: 6px;
  background-color: var(--form-input-bg);
  box-shadow: var(--shadow-sm);
  color: var(--form-input-text);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control.date-input:focus {
  outline: none;
  border-color: var(--form-input-border-focus);
  box-shadow: 0 0 0 3px rgba(212, 163, 115, 0.2);
}

.is-invalid.date-input-invalid {
  border-color: var(--color-error);
}
.is-invalid.date-input-invalid:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}


.invalid-input {
  opacity: 0;
  color: var(--color-error);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  transform: translateY(-4px);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.invalid-input.show-error {
  opacity: 1;
  transform: translateY(0);
}

.red-font {
  color: var(--color-error);
}

.show-data {
  margin-bottom: 0.5rem;
  color: var(--color-text-primary);
}

.x-error-msg-text {
  font-size: 0.875rem;
}

.date-input-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.calendar-icon {
  position: absolute;
  right: 0.75rem; /* Adjusted for better alignment inside input */
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: var(--color-tertiary); /* Using theme color */
  font-size: 1.2rem;
}

/* Override global datepicker styles */
:host ::ng-deep .mat-datepicker-content {
  background-color: var(--form-input-bg) !important;
  color: var(--form-input-text) !important;
  border-radius: 8px !important;
  border: 1px solid var(--form-input-border) !important;
  box-shadow: var(--shadow-lg) !important;
}

:host ::ng-deep .mat-calendar {
  background-color: var(--form-input-bg) !important;
}

:host ::ng-deep .mat-calendar-header {
  background-color: var(--color-tertiary) !important;
  color: var(--color-text-on-tertiary) !important;
}

:host ::ng-deep .mat-calendar-body-cell-content {
  color: var(--form-input-text) !important;
}

:host ::ng-deep .mat-calendar-body-today {
  border-color: var(--color-tertiary) !important;
}

:host ::ng-deep .mat-calendar-body-selected {
  background-color: var(--color-tertiary) !important;
  color: var(--color-text-on-tertiary) !important;
}

:host ::ng-deep .mat-calendar-body-cell:hover .mat-calendar-body-cell-content {
  background-color: var(--color-tertiary-shade) !important;
  color: var(--color-tertiary) !important;
}

:host ::ng-deep .mat-calendar-arrow {
  border-top-color: var(--color-text-on-tertiary) !important;
}

:host ::ng-deep .mat-calendar-period-button {
  color: var(--color-text-on-tertiary) !important;
}

:host ::ng-deep .mat-calendar-next-button,
:host ::ng-deep .mat-calendar-previous-button {
  color: var(--color-text-on-tertiary) !important;
}





