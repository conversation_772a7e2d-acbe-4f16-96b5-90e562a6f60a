.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid transparent;
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.25rem;
  cursor: pointer;
  transition: all 250ms ease;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(66, 111, 243, 0.2);
  }

  // Sizes
  &--small {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    line-height: 1rem;
  }

  &--medium {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  &--large {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    line-height: 1.5rem;
  }

  &--full-width {
    width: 100%;
  }

  // Primary Button
  &--primary {
    background-color: var(--color-tertiary);
    color: white;
    border-color: var(--color-tertiary);

    &:hover:not(.btn--disabled) {
      background-color: var(--color-secondary);
      border-color: var(--color-secondary);
    }

    &:active:not(.btn--disabled) {
      background-color: var(--color-secondary);
      border-color: var(--color-secondary);
    }
  }

  // Secondary Button
  &--secondary {
    background-color: var(--color-secondary-shade);
    color: var(--color-tertiary);
    border-color: var(--color-secondary);

    &:hover:not(.btn--disabled) {
      background-color: var(--color-secondary);
    }

    &:active:not(.btn--disabled) {
      background-color: var(--color-secondary);
    }
  }

  // Outline Button
  &--outline {
    background-color: transparent;
    color: var(--color-tertiary);
    border-color: var(--color-tertiary);

    &:hover:not(.btn--disabled) {
      background-color: var(--color-tertiary-shade);
    }

    &:active:not(.btn--disabled) {
      background-color: var(--color-secondary-shade);
    }
  }

  // Ghost Button
  &--ghost {
    background-color: transparent;
    color: var(--color-tertiary);
    border-color: transparent;

    &:hover:not(.btn--disabled) {
      background-color: var(--color-tertiary-shade);
    }

    &:active:not(.btn--disabled) {
      background-color: var(--color-secondary-shade);
    }
  }

  // Danger Button
  &--danger {
    background-color: var(--color-poppy-red);
    color: white;
    border-color: var(--color-poppy-red);

    &:hover:not(.btn--disabled) {
      background-color: var(--color-poppy-orange);
      border-color: var(--color-poppy-orange);
    }

    &:active:not(.btn--disabled) {
      background-color: var(--color-poppy-orange);
      border-color: var(--color-poppy-orange);
    }
  }

  // Disabled state
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  // Icon spacing
  i + span {
    margin-left: 0.5rem;
  }
}

.ml-2 {
  margin-left: 0.5rem;
}
