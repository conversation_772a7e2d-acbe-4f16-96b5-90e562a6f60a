.form-group {
  margin-bottom: 1.25rem; /* Slightly increased for better spacing */
}
label {
  margin-bottom: 0.5rem; /* Controls space between label and input */
}
.bold-label {
  font-weight: 500 !important; /* Bold when readonly */
}

.form-control {
  border: 1px solid var(--color-secondary);
  border-radius: 6px;
  background-color: var(--color-secondary-shade);
  color: var(--color-text-dark);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--color-tertiary);
  box-shadow: 0 0 0 3px rgba(212, 163, 115, 0.2);
}

.is-invalid {
  border-color: var(--color-poppy-red);
}

.is-invalid:focus {
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2);
}

.invalid-input {
  opacity: 0;
  color: var(--color-poppy-red);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  transform: translateY(-4px);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.invalid-input.show-error {
  opacity: 1;
  transform: translateY(0);
}

.red-font {
  color: var(--color-poppy-red);
}

.show-data {
  margin-bottom: 0.8rem;
  color: var(--color-text-dark);
  font-size: 1rem;
}

.x-error-msg-text {
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Ensure Material components align with theme */
:host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
  background-color: var(--color-background, #ffffff); /* White background for Material inputs */
  border-radius: 6px;
}

:host ::ng-deep .mat-form-field-underline {
  display: none; /* Remove Material underline for consistency */
}

:host ::ng-deep .mat-form-field-infix {
  padding: 0.5rem 0;
}

