.form-group {
  margin-bottom: 1.25rem; /* Slightly increased for better spacing */
}
label {
  margin-bottom: 0.5rem; /* Controls space between label and input */
}
.bold-label {
  font-weight: 500 !important; /* Bold when readonly */
}

.form-control {
  border: 1px solid var(--form-input-border); /* Using theme border color */
  border-radius: 6px; /* Slightly tighter radius for modern look */
  background-color: var(--form-input-bg); /* Using theme background */
  box-shadow: var(--shadow-sm); /* Using theme shadow */
  color: var(--form-input-text); /* Using theme text color */
  padding: 0.75rem 1rem; /* Comfortable padding */
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease; /* Smooth transitions */
}

.form-control:focus {
  outline: none;
  border-color: var(--form-input-border-focus); /* Using theme focus color */
  box-shadow: 0 0 0 3px rgba(212, 163, 115, 0.2); /* Tertiary color glow effect */
}

.is-invalid {
  border-color: var(--color-error); /* Error state border */
}

.is-invalid:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2); /* Error state focus glow */
}

.invalid-input {
  opacity: 0; /* Hidden by default */
  color: var(--color-error);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  transform: translateY(-4px); /* Slight upward shift for animation */
  transition: opacity 0.2s ease, transform 0.2s ease; /* Smooth fade-in */
}

.invalid-input.show-error {
  opacity: 1; /* Visible on hover */
  transform: translateY(0); /* Slide into place */
}

.red-font {
  color: var(--color-error); /* Mandatory field indicator */
}

.show-data {
  margin-bottom: 0.8rem;
  color: var(--color-text-primary);
  font-size: 1rem;
}

.x-error-msg-text {
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Ensure Material components align with theme */
:host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
  background-color: var(--form-input-bg); /* Using theme background for Material inputs */
  border-radius: 6px;
  border: 1px solid var(--form-input-border);
}

:host ::ng-deep .mat-form-field-appearance-fill:not(.mat-form-field-disabled) .mat-form-field-flex:hover {
  background-color: var(--form-input-bg);
  border-color: var(--color-tertiary);
}

:host ::ng-deep .mat-form-field-appearance-fill.mat-focused .mat-form-field-flex {
  background-color: var(--form-input-bg);
  border-color: var(--form-input-border-focus);
}

:host ::ng-deep .mat-form-field-underline {
  display: none; /* Remove Material underline for consistency */
}

:host ::ng-deep .mat-form-field-infix {
  padding: 0.5rem 0;
}

:host ::ng-deep .mat-form-field-label {
  color: var(--form-label-text);
}

:host ::ng-deep .mat-input-element {
  color: var(--form-input-text);
}

:host ::ng-deep .mat-input-element::placeholder {
  color: var(--color-text-muted);
}

