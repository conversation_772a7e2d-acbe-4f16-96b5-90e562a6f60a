.form-group {
  position: relative;
  margin-bottom: 1.25rem;
}

label {
  margin-bottom: 0.5rem; /* Controls space between label and input */
}
.bold-label {
  font-weight: 500 !important; /* Bold when readonly */
}



.red-font {
  color: var(--color-error);
  font-weight: 700;
  margin-left: 0.25rem;
}

.invalid-input {
  opacity: 0;
  color: var(--color-error);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  transform: translateY(-4px);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.invalid-input.show-error {
  opacity: 1;
  transform: translateY(0);
}

.error-msg-text {
  font-size: 0.875rem;
  line-height: 1.5;
}

.show-data {
  color: var(--color-text-primary);
  background-color: var(--form-input-bg);
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--form-input-border);
}

.w-100 {
  width: 100%;
}

:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  border: 1px solid var(--form-input-border);
  border-radius: 6px;
  background-color: var(--form-input-bg);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline:hover,
:host ::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline {
  border-color: var(--form-input-border-focus);
  box-shadow: 0 0 0 3px rgba(212, 163, 115, 0.2);
}

:host ::ng-deep .mat-form-field-appearance-outline.is-invalid .mat-form-field-outline {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

:host ::ng-deep .mat-form-field-flex {
  background-color: var(--form-input-bg);
  border-radius: 6px;
}

:host ::ng-deep .mat-form-field-infix {
  padding: 0.5rem 0;
}

:host ::ng-deep .mat-form-field-label {
  color: var(--form-label-text);
}

:host ::ng-deep .mat-select-value,
:host ::ng-deep .mat-select-arrow {
  color: var(--form-input-text);
}

:host ::ng-deep .mat-select-panel {
  background-color: var(--form-input-bg);
  border-radius: 6px;
  box-shadow: var(--shadow-lg);
  margin-top: 4px;
  border: 1px solid var(--form-input-border);
}

:host ::ng-deep .mat-option {
  color: var(--form-input-text);
  font-size: 0.875rem;
}

:host ::ng-deep .mat-option.mat-selected:not(.mat-option-disabled) {
  background-color: var(--color-tertiary);
  color: var(--color-text-on-tertiary);
}

:host ::ng-deep .mat-option:hover:not(.mat-option-disabled),
:host ::ng-deep .mat-option.mat-active {
  background-color: var(--color-tertiary-shade);
  color: var(--color-tertiary);
}
