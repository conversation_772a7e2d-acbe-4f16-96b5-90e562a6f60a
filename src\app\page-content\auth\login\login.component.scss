/* =====================
   Login Component Styles
   Using global theme variables from styles.scss
====================== */

/* =====================
   Login Container Styles
====================== */

.login-container {
  min-height: 100vh;
  background: var(--color-background-primary);
  font-family: 'Open Sans', sans-serif;
}

.navbar {
  background: var(--color-secondary);
  border-bottom: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-sm);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-brand h2,
.nav-link,
.nav-toggle span {
  color: var(--color-tertiary);
}

.nav-link:hover,
.nav-link.active {
  color: var(--color-tertiary-hover);
}

.nav-link.active::after {
  background: var(--color-tertiary);
}

.main-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 80px);
  padding: 2rem;
}

.login-card {
  background: var(--color-background-primary);
  padding: 2.5rem;
  border-radius: 20px;
  border: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-lg);
  max-width: 400px;
  width: 100%;
}

.login-header h1 {
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
}

.login-header p,
.checkbox-container,
.signup-link p {
  color: var(--color-text-secondary);
}

.form-label {
  color: var(--form-label-text);
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.form-input {
  background: var(--form-input-bg);
  border: 2px solid var(--form-input-border);
  color: var(--form-input-text);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  width: 100%;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;

  &:focus {
    border-color: var(--form-input-border-focus);
    box-shadow: 0 0 0 3px rgba(212, 163, 115, 0.2);
    outline: none;
  }

  &.error {
    border-color: var(--color-error);
    background: rgba(239, 68, 68, 0.1);
  }

  &::placeholder {
    color: var(--color-text-muted);
  }
}

.password-toggle {
  color: var(--color-text-secondary);

  &:hover {
    color: var(--color-tertiary);
  }
}

.error-message {
  color: var(--color-error);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.forgot-password,
.signup-text {
  color: var(--color-tertiary);
  text-decoration: none;
  font-weight: 500;

  &:hover {
    color: var(--color-tertiary-hover);
    text-decoration: underline;
  }
}

.login-button {
  background: var(--button-primary-bg);
  color: var(--button-primary-text);
  border: none;
  border-radius: 8px;
  padding: 0.875rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  width: 100%;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: var(--button-primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  &:active:not(:disabled) {
    background: var(--button-primary-active);
    transform: translateY(0);
  }

  &:disabled {
    background: var(--color-secondary-shade);
    color: var(--color-text-muted);
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
  }
}

.spinner {
  border: 2px solid transparent;
  border-top-color: var(--color-text-on-tertiary);
  border-radius: 50%;
  width: 1rem;
  height: 1rem;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .nav-toggle {
    display: flex;
  }

  .main-content {
    padding: 1rem;
    min-height: calc(100vh - 60px);
  }

  .login-card {
    padding: 2rem;
    border-radius: 15px;
  }

  .login-header h1 {
    font-size: 1.75rem;
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .nav-content {
    padding: 0 1rem;
  }

  .login-card {
    padding: 1.5rem;
    margin: 0.5rem;
  }

  .login-header h1 {
    font-size: 1.5rem;
  }

  .form-input {
    padding: 0.625rem 0.875rem;
  }

  .login-button {
    padding: 0.75rem;
  }
}
