.login-container {
  min-height: 100vh;
  background: var(--color-primary);
  font-family: 'Open Sans', sans-serif;
}

/* Navigation Bar */
.navbar {
  background: var(--color-secondary);
  border-bottom: 1px solid var(--color-secondary-shade);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand h2 {
  color: var(--color-brand-primary);
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: var(--color-nav-link-text);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  padding: 0.5rem 0;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--color-nav-link-hover);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--color-brand-primary);
  border-radius: 1px;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.nav-toggle span {
  width: 25px;
  height: 3px;
  background: var(--color-brand-primary);
  margin: 3px 0;
  transition: 0.3s;
}

/* Main Content */
.main-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 80px);
  padding: 2rem;
}

.login-card {
  background: var(--color-card-bg);
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px var(--shadow-medium);
  border: 1px solid var(--color-border-light);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  font-size: 2rem;
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.login-header p {
  color: var(--color-text-secondary);
  font-size: 0.9rem;
  margin: 0;
}

/* Form Styles */
.login-form {
  display: flex;
  flex-direction: column;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--color-text-primary);
  font-weight: 500;
  font-size: 0.9rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--color-form-input-border);
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  background: var(--color-form-input-bg);
  color: var(--color-form-input-text);
}

.form-input::placeholder {
  color: var(--color-form-input-placeholder);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-form-input-border-focus);
  box-shadow: 0 0 0 3px var(--shadow-focus);
}

.form-input:hover:not(:focus) {
  border-color: var(--color-form-input-border-hover);
}

.form-input.error {
  border-color: var(--color-form-input-error);
  background: var(--color-red-50);
}

.password-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: var(--color-text-secondary);
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: var(--color-brand-primary);
}

.error-message {
  color: var(--color-form-input-error);
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.checkbox-container input {
  margin-right: 0.5rem;
  cursor: pointer;
  accent-color: var(--color-brand-primary);
}

.forgot-password {
  color: var(--color-text-link);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: var(--color-text-link-hover);
  text-decoration: underline;
}

.login-button {
  width: 100%;
  padding: 0.875rem;
  background: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.login-button:hover:not(:disabled) {
  background: var(--color-btn-primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px var(--shadow-medium);
}

.login-button:active:not(:disabled) {
  background: var(--color-btn-primary-active);
  transform: translateY(0);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--color-utility-disabled);
  color: var(--color-utility-disabled-text);
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--color-btn-primary-text);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.signup-link {
  text-align: center;
  margin-top: 1.5rem;
}

.signup-link p {
  color: var(--color-text-secondary);
  font-size: 0.9rem;
  margin: 0;
}

.signup-text {
  color: var(--color-text-link);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.signup-text:hover {
  color: var(--color-text-link-hover);
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .nav-toggle {
    display: flex;
  }

  .main-content {
    padding: 1rem;
    min-height: calc(100vh - 60px);
  }

  .login-card {
    padding: 2rem;
    border-radius: 15px;
  }

  .login-header h1 {
    font-size: 1.75rem;
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .nav-content {
    padding: 0 1rem;
  }

  .login-card {
    padding: 1.5rem;
    margin: 0.5rem;
  }

  .login-header h1 {
    font-size: 1.5rem;
  }

  .form-input {
    padding: 0.625rem 0.875rem;
  }

  .login-button {
    padding: 0.75rem;
  }
}
