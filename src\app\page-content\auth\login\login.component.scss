@use '@angular/material' as mat;
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap');
@import url('https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.css');

/* =====================
   Root Variables
====================== */
:root {
  --color-primary: #fefae0;
  --color-secondary: #ccd5ae;
  --color-secondary-shade: #e9edc9;
  --color-tertiary: #d4a373;
  --color-tertiary-shade: #faedcd;
}

/* =====================
   SCSS Variables
====================== */
$color-primary: var(--color-primary);
$color-secondary: var(--color-secondary);
$color-secondary-shade: var(--color-secondary-shade);
$color-tertiary: var(--color-tertiary);
$color-tertiary-shade: var(--color-tertiary-shade);

/* =====================
   Angular Material Theme Setup
====================== */
html {
  @include mat.core();
  @include mat.all-component-themes(
    mat.define-light-theme((
      color: (
        primary: mat.define-palette(mat.$orange-palette, 400),
        accent: mat.define-palette(mat.$green-palette, 300),
        warn: mat.define-palette(mat.$red-palette),
      ),
      typography: mat.define-typography-config(),
      density: 0
    ))
  );
}

/* =====================
   Global Styles
====================== */
body {
  font-family: 'Open Sans', sans-serif;
  background-color: $color-primary;
  color: $color-secondary;
  margin: 0;
}

.main-container {
  padding: 2rem;
  background-color: $color-primary;
  color: $color-secondary;
}

.page-layout {
  background-color: $color-tertiary-shade;
}

button {
  background-color: $color-tertiary;
  color: white;

  &:hover {
    background-color: darken($color-tertiary, 10%);
  }
}

input, textarea, select {
  background-color: $color-secondary-shade;
  border: 1px solid $color-secondary;
  color: $color-secondary;

  &:focus {
    border-color: $color-tertiary;
    outline: none;
  }
}

.text-muted {
  color: $color-secondary;
}

/* =====================
   Login and Navbar Styling
====================== */
.login-container {
  min-height: 100vh;
  background: $color-primary;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar {
  background: $color-secondary;
  border-bottom: 1px solid $color-secondary-shade;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-brand h2,
.nav-link,
.nav-toggle span {
  color: $color-tertiary;
}

.nav-link:hover,
.nav-link.active {
  color: darken($color-tertiary, 10%);
}

.nav-link.active::after {
  background: $color-tertiary;
}

.main-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 80px);
  padding: 2rem;
}

.login-card {
  background: $color-primary;
  padding: 2.5rem;
  border-radius: 20px;
  border: 1px solid $color-secondary-shade;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
}

.login-header h1 {
  color: $color-secondary;
}

.login-header p,
.checkbox-container,
.signup-link p {
  color: lighten($color-secondary, 10%);
}

.form-label,
.form-input {
  color: $color-secondary;
}

.form-input {
  background: $color-secondary-shade;
  border: 2px solid $color-secondary;

  &:focus {
    border-color: $color-tertiary;
    box-shadow: 0 0 0 3px rgba($color-tertiary, 0.3);
  }

  &.error {
    border-color: red;
    background: lighten(red, 40%);
  }
}

.password-toggle {
  color: lighten($color-secondary, 10%);

  &:hover {
    color: $color-tertiary;
  }
}

.error-message {
  color: red;
}

.forgot-password,
.signup-text {
  color: $color-tertiary;

  &:hover {
    color: darken($color-tertiary, 10%);
  }
}

.login-button {
  background: $color-tertiary;
  color: white;

  &:hover:not(:disabled) {
    background: darken($color-tertiary, 10%);
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }

  &:active:not(:disabled) {
    background: darken($color-tertiary, 15%);
  }

  &:disabled {
    background: $color-secondary-shade;
    color: darken($color-secondary, 20%);
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.spinner {
  border-top-color: white;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .nav-toggle {
    display: flex;
  }

  .main-content {
    padding: 1rem;
    min-height: calc(100vh - 60px);
  }

  .login-card {
    padding: 2rem;
    border-radius: 15px;
  }

  .login-header h1 {
    font-size: 1.75rem;
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .nav-content {
    padding: 0 1rem;
  }

  .login-card {
    padding: 1.5rem;
    margin: 0.5rem;
  }

  .login-header h1 {
    font-size: 1.5rem;
  }

  .form-input {
    padding: 0.625rem 0.875rem;
  }

  .login-button {
    padding: 0.75rem;
  }
}
