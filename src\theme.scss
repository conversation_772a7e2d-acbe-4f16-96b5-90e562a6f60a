@use '@angular/material' as mat;

@include mat.core();

// Define custom color palettes using the simple color variables
$custom-primary-palette: (
  50: #faedcd,   // tertiary-shade
  100: #e9edc9,  // secondary-shade
  200: #d4a373,  // tertiary
  300: #ccd5ae,  // secondary
  400: #d4a373,  // tertiary (main)
  500: #d4a373,  // tertiary (main)
  600: #ccd5ae,  // secondary
  700: #a4b386,  // darker secondary
  800: #8a9c6e,  // even darker
  900: #6f8055,  // darkest
  A100: #faedcd,
  A200: #e9edc9,
  A400: #d4a373,
  A700: #ccd5ae,
  contrast: (
    50: rgba(26, 26, 26, 1),
    100: rgba(26, 26, 26, 1),
    200: rgba(255, 255, 255, 1),
    300: rgba(26, 26, 26, 1),
    400: rgba(255, 255, 255, 1),
    500: rgba(255, 255, 255, 1),
    600: rgba(26, 26, 26, 1),
    700: rgba(255, 255, 255, 1),
    800: rgba(255, 255, 255, 1),
    900: rgba(255, 255, 255, 1),
    A100: rgba(26, 26, 26, 1),
    A200: rgba(26, 26, 26, 1),
    A400: rgba(255, 255, 255, 1),
    A700: rgba(26, 26, 26, 1),
  )
);

$primary: mat.define-palette($custom-primary-palette, 500);
$accent: mat.define-palette($custom-primary-palette, 300);
$warn: mat.define-palette((
  50: #ffebee,
  100: #ffcdd2,
  200: #ef9a9a,
  300: #e57373,
  400: #ef5350,
  500: #ff6b6b,  // poppy-red
  600: #e53935,
  700: #d32f2f,
  800: #c62828,
  900: #b71c1c,
  A100: #ff8a80,
  A200: #ff5252,
  A400: #ff1744,
  A700: #d50000,
  contrast: (
    50: rgba(0, 0, 0, 0.87),
    100: rgba(0, 0, 0, 0.87),
    200: rgba(0, 0, 0, 0.87),
    300: rgba(0, 0, 0, 0.87),
    400: rgba(0, 0, 0, 0.87),
    500: rgba(255, 255, 255, 0.87),
    600: rgba(255, 255, 255, 0.87),
    700: rgba(255, 255, 255, 0.87),
    800: rgba(255, 255, 255, 0.87),
    900: rgba(255, 255, 255, 0.87),
    A100: rgba(0, 0, 0, 0.87),
    A200: rgba(255, 255, 255, 0.87),
    A400: rgba(255, 255, 255, 0.87),
    A700: rgba(255, 255, 255, 0.87),
  )
), 500);

$theme: mat.define-light-theme((
  color: (
    primary: $primary,
    accent: $accent,
    warn: $warn,
  ),
  typography: mat.define-typography-config(),
  density: 0
));

@include mat.all-component-themes($theme);

// Custom overrides for Material components
.mat-mdc-form-field {
  --mdc-filled-text-field-container-color: var(--color-secondary-shade);
  --mdc-filled-text-field-focus-active-indicator-color: var(--color-tertiary);
  --mdc-filled-text-field-hover-active-indicator-color: var(--color-tertiary);
  --mdc-filled-text-field-input-text-color: var(--color-text-dark);
  --mdc-filled-text-field-label-text-color: var(--color-text-medium);
}

.mat-mdc-button.mat-primary {
  --mdc-text-button-label-text-color: var(--color-tertiary);
}

.mat-mdc-raised-button.mat-primary {
  --mdc-protected-button-container-color: var(--color-tertiary);
  --mdc-protected-button-label-text-color: white;
}

.mat-mdc-raised-button.mat-primary:hover {
  --mdc-protected-button-container-color: var(--color-secondary);
}
