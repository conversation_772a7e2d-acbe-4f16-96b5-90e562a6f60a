@use '@angular/material' as mat;

@include mat.core();

// Import the color variables from styles.scss
:root {
  --color-primary: #fefae0;
  --color-secondary: #ccd5ae;
  --color-secondary-shade: #e9edc9;
  --color-tertiary: #d4a373;
  --color-tertiary-shade: #faedcd;
  --color-tertiary-hover: #c3885e;
}

// Define custom color palettes based on your theme colors
$custom-primary-palette: (
  50: #fefae0,   // Your primary color (lightest)
  100: #faedcd,  // Your tertiary-shade
  200: #e9edc9,  // Your secondary-shade
  300: #d4a373,  // Your tertiary color
  400: #ccd5ae,  // Your secondary color
  500: #d4a373,  // Main tertiary color (primary action color)
  600: #c3885e,  // Your tertiary-hover
  700: #b07c54,  // Darker version
  800: #9d6f4a,  // Even darker
  900: #8a6240,  // Darkest
  A100: #faedcd,
  A200: #e9edc9,
  A400: #d4a373,
  A700: #c3885e,
  contrast: (
    50: rgba(0, 0, 0, 0.87),
    100: rgba(0, 0, 0, 0.87),
    200: rgba(0, 0, 0, 0.87),
    300: rgba(0, 0, 0, 0.87),
    400: rgba(0, 0, 0, 0.87),
    500: rgba(255, 255, 255, 0.87),
    600: rgba(255, 255, 255, 0.87),
    700: rgba(255, 255, 255, 0.87),
    800: rgba(255, 255, 255, 0.87),
    900: rgba(255, 255, 255, 0.87),
    A100: rgba(0, 0, 0, 0.87),
    A200: rgba(0, 0, 0, 0.87),
    A400: rgba(255, 255, 255, 0.87),
    A700: rgba(255, 255, 255, 0.87),
  )
);

$custom-accent-palette: (
  50: #e9edc9,   // Your secondary-shade (lightest)
  100: #ccd5ae,  // Your secondary color
  200: #b8c49a,  // Slightly darker
  300: #a4b386,  // Darker
  400: #90a272,  // Even darker
  500: #ccd5ae,  // Main secondary color
  600: #7c915e,  // Darker version
  700: #68804a,  // Even darker
  800: #546f36,  // Very dark
  900: #405e22,  // Darkest
  A100: #e9edc9,
  A200: #ccd5ae,
  A400: #90a272,
  A700: #7c915e,
  contrast: (
    50: rgba(0, 0, 0, 0.87),
    100: rgba(0, 0, 0, 0.87),
    200: rgba(0, 0, 0, 0.87),
    300: rgba(0, 0, 0, 0.87),
    400: rgba(0, 0, 0, 0.87),
    500: rgba(0, 0, 0, 0.87),
    600: rgba(255, 255, 255, 0.87),
    700: rgba(255, 255, 255, 0.87),
    800: rgba(255, 255, 255, 0.87),
    900: rgba(255, 255, 255, 0.87),
    A100: rgba(0, 0, 0, 0.87),
    A200: rgba(0, 0, 0, 0.87),
    A400: rgba(0, 0, 0, 0.87),
    A700: rgba(255, 255, 255, 0.87),
  )
);

// Create the theme using custom palettes
$primary: mat.define-palette($custom-primary-palette, 500);
$accent: mat.define-palette($custom-accent-palette, 500);
$warn: mat.define-palette(mat.$red-palette);

$theme: mat.define-light-theme((
  color: (
    primary: $primary,
    accent: $accent,
    warn: $warn,
  ),
  typography: mat.define-typography-config(),
  density: 0
));

@include mat.all-component-themes($theme);

// Custom overrides for Material components to use your exact colors
.mat-mdc-form-field {
  --mdc-filled-text-field-container-color: var(--color-secondary-shade);
  --mdc-filled-text-field-focus-active-indicator-color: var(--color-tertiary);
  --mdc-filled-text-field-hover-active-indicator-color: var(--color-tertiary);
  --mdc-filled-text-field-input-text-color: var(--color-secondary);
  --mdc-filled-text-field-label-text-color: var(--color-secondary);
}

.mat-mdc-button.mat-primary {
  --mdc-text-button-label-text-color: var(--color-tertiary);
}

.mat-mdc-raised-button.mat-primary {
  --mdc-protected-button-container-color: var(--color-tertiary);
  --mdc-protected-button-label-text-color: white;
}

.mat-mdc-raised-button.mat-primary:hover {
  --mdc-protected-button-container-color: var(--color-tertiary-hover);
}
