@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap');
@import url('https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.css');

/* =====================
   Root Variables
====================== */
:root {
  /* Main Theme Colors */
  --color-primary: #fefae0;        /* 60% main color - light cream background */
  --color-secondary: #ccd5ae;      /* 30% secondary color - sage green */
  --color-secondary-shade: #e9edc9; /* highlight color for the 30% */
  --color-tertiary: #d4a373;       /* 10% focus color - warm brown */
  --color-tertiary-shade: #faedcd; /* shade and focus color of the 10% */
  --color-tertiary-hover: #c3885e; /* manually darkened version for hover states */

  /* Extended Color Palette */
  --color-tertiary-dark: #b07c54;  /* Darker version of tertiary */
  --color-secondary-dark: #a4b386; /* Darker version of secondary */
  --color-primary-dark: #f5f0d0;   /* Slightly darker primary for subtle variations */

  /* Text Colors */
  --color-text-primary: #2d3748;   /* Dark text on light backgrounds */
  --color-text-secondary: #4a5568; /* Secondary text color */
  --color-text-muted: #718096;     /* Muted text color */
  --color-text-on-tertiary: #ffffff; /* White text on tertiary background */

  /* Border Colors */
  --color-border-light: var(--color-secondary-shade);
  --color-border-medium: var(--color-secondary);
  --color-border-focus: var(--color-tertiary);

  /* Background Colors */
  --color-background-primary: var(--color-primary);
  --color-background-secondary: var(--color-secondary-shade);
  --color-background-tertiary: var(--color-tertiary-shade);

  /* Button Theme Variables */
  --button-primary-bg: var(--color-tertiary);
  --button-primary-text: var(--color-text-on-tertiary);
  --button-primary-hover: var(--color-tertiary-hover);
  --button-primary-active: var(--color-tertiary-dark);

  --button-secondary-bg: var(--color-secondary-shade);
  --button-secondary-text: var(--color-tertiary);
  --button-secondary-border: var(--color-secondary);
  --button-secondary-hover: var(--color-secondary);
  --button-secondary-active: var(--color-secondary-dark);

  --button-outline-bg: transparent;
  --button-outline-text: var(--color-tertiary);
  --button-outline-border: var(--color-tertiary);
  --button-outline-hover: var(--color-tertiary-shade);
  --button-outline-active: var(--color-secondary-shade);

  --button-ghost-bg: transparent;
  --button-ghost-text: var(--color-tertiary);
  --button-ghost-hover: var(--color-tertiary-shade);
  --button-ghost-active: var(--color-secondary-shade);

  --button-danger-bg: #dc3545;
  --button-danger-text: white;
  --button-danger-hover: #c82333;
  --button-danger-active: #bd2130;

  /* Form Element Variables */
  --form-input-bg: var(--color-secondary-shade);
  --form-input-border: var(--color-secondary);
  --form-input-border-focus: var(--color-tertiary);
  --form-input-text: var(--color-text-primary);
  --form-label-text: var(--color-text-secondary);

  /* Shadow Variables */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Status Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: var(--color-tertiary);
}

/* =====================
   Global Styles
====================== */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Open Sans', sans-serif;
  background-color: var(--color-background-primary);
  color: var(--color-text-primary);
  margin: 0;
  line-height: 1.6;
}

.main-container {
  padding: 2rem;
  background-color: var(--color-background-primary);
  color: var(--color-text-primary);
  min-height: 100vh;
}

.page-layout {
  background-color: var(--color-background-tertiary);
  min-height: 100vh;
}

/* =====================
   Form Elements Global Styling
====================== */
button:not(.mat-mdc-button):not(.mat-mdc-raised-button):not(.mat-mdc-outlined-button):not(.btn) {
  background-color: var(--button-primary-bg); 
  color: var(--button-primary-text);
  border: 1px solid var(--button-primary-bg);
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background-color: var(--button-primary-hover);
    border-color: var(--button-primary-hover);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

input:not(.mat-mdc-input-element),
textarea:not(.mat-mdc-input-element),
select:not(.mat-mdc-select) {
  background-color: var(--form-input-bg);
  border: 1px solid var(--form-input-border);
  color: var(--form-input-text);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;

  &:focus {
    border-color: var(--form-input-border-focus);
    outline: none;
    box-shadow: 0 0 0 3px rgba(212, 163, 115, 0.1);
  }

  &::placeholder {
    color: var(--color-text-muted);
  }
}

/* =====================
   Utility Classes
====================== */
.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-muted { color: var(--color-text-muted); }
.text-tertiary { color: var(--color-tertiary); }

.bg-primary { background-color: var(--color-background-primary); }
.bg-secondary { background-color: var(--color-background-secondary); }
.bg-tertiary { background-color: var(--color-background-tertiary); }

.border-light { border-color: var(--color-border-light); }
.border-medium { border-color: var(--color-border-medium); }
.border-focus { border-color: var(--color-border-focus); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
