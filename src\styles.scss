@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap');
@import url('https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.css');

/* =====================
   Root Variables
====================== */
:root {
  --color-primary: #fefae0;
  --color-secondary: #ccd5ae;
  --color-secondary-shade: #e9edc9;
  --color-tertiary: #d4a373;
  --color-tertiary-shade: #faedcd;
  --color-tertiary-hover: #c3885e; // manually darkened version
}

/* =====================
   Global Styles
====================== */
body {
  font-family: 'Open Sans', sans-serif;
  background-color: var(--color-primary);
  color: var(--color-secondary);
  margin: 0;
}

.main-container {
  padding: 2rem;
  background-color: var(--color-primary);
  color: var(--color-secondary);
}

.page-layout {
  background-color: var(--color-tertiary-shade);
}

button {
  background-color: var(--color-tertiary);
  color: white;

  &:hover {
    background-color: var(--color-tertiary-hover);
  }
}

input,
textarea,
select {
  background-color: var(--color-secondary-shade);
  border: 1px solid var(--color-secondary);
  color: var(--color-secondary);

  &:focus {
    border-color: var(--color-tertiary);
    outline: none;
  }
}

/* Utility */
.text-muted {
  color: var(--color-secondary);
}
